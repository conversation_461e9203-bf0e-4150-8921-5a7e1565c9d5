<div class="container-fluid py-4" style="max-width: 1200px;">
	<!-- Header Section -->
	<div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
		<div class="d-flex align-items-center mb-3 mb-md-0">
			<div>
				<h1 class="h3 font-weight-bold mb-1">
					{% if type == 'devis' %}
						DEVIS -
						<span class="badge badge-warning rounded-pill px-3 py-1">{{ reservation.numero }}</span>
					{% else %}
						RESERVATION -
						<span class="badge badge-warning rounded-pill px-3 py-1">{{ reservation.reference }}</span>
					{% endif %}
				</h1>
				<small class="">C<PERSON>é le
					{% if type == 'devis' %}
						{{ reservation.dateCreation|date('d/m/Y') }}
					{% else %}
						{{ reservation.dateReservation|date('d/m/Y') }}
					{% endif %}
				</small>
			</div>
		</div>
		<a href="{{ path('client_reservations') }}" class="btn btn-outline-danger d-flex align-items-center">
			<i class="fas fa-reply mr-2"></i>
			Mes Réservations
		</a>
	</div>
