{% extends 'baseClient.html.twig' %}

{% block title %}Détails reservation
{% endblock %}

{% block stylesheets %}
	<!-- Bootstrap 4 CSS -->
	<link
	rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css">
	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
	<link rel="stylesheet" href="{{ asset('css/client/reservation/mes_reservations/details_resa.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
	{% set type = 'reservation' %}

	{% include 'client/reservation/details/_header.html.twig' with {
		'type': type,
		'reservation': reservation
	} %}

	{% include 'client/reservation/details/_main_content.html.twig' with {
	'type': type,
	'reservation': reservation,
	'prixConductSuppl': prixConductSuppl|default(null)
} %}

	{% include "client/reservation/modalConducteur.html.twig" %}
{% endblock %}

{% block javascripts %}
	{% include 'client/reservation/details/_scripts.html.twig' with {
		'type': type
	} %}
{% endblock %}
<div
	class="card reservation-card mb-3 shadow-sm">
	<!-- Rental Period Section -->
	<div class="card-body p-4">
		<h5 class="card-title font-weight-bold text-red-800 d-flex align-items-center mb-4">
			<i class="fas fa-calendar-alt text-red-400 mr-2"></i>
			Période de Location
		</h5>

		<div
			class="row">
			<!-- Left Column: Departure and Return -->
			<div
				class="col-md-6 pr-md-4">
				<!-- Departure Information -->
				<div class="mb-4 pb-3 border-bottom border-light">
					<div class="d-flex align-items-center mb-2">
						<div class="bg-light rounded-circle p-2 mr-3">
							<i class="fas fa-plane-departure "></i>
						</div>
						<h6 class="font-weight-bold  mb-0">Départ</h6>
					</div>
					<div class="ml-4 pl-3">
						<p class="mb-2">
							<i class="fas fa-calendar-alt  mr-2"></i>
							<span class="font-weight-medium">{{reservation.dateDebut|date('d/m/Y')}}</span>
							à
							<span class="font-weight-medium">{{reservation.dateDebut|date('H:i')}}</span>
						</p>
						<p class="mb-0">
							<i class="fas fa-map-marker-alt  mr-2"></i>
							<span>{{reservation.agenceDepart}}</span>
						</p>
					</div>
				</div>

				<!-- Return Information -->
				<div class="mb-3">
					<div class="d-flex align-items-center mb-2">
						<div class="bg-light rounded-circle p-2 mr-3">
							<i class="fas fa-plane-arrival "></i>
						</div>
						<h6 class="font-weight-bold  mb-0">Retour</h6>
					</div>
					<div class="ml-4 pl-3">
						<p class="mb-2">
							<i class="fas fa-calendar-alt  mr-2"></i>
							<span class="font-weight-medium">{{reservation.dateFin|date('d/m/Y')}}</span>
							à
							<span class="font-weight-medium">{{reservation.dateFin|date('H:i')}}</span>
						</p>
						<p class="mb-0">
							<i class="fas fa-map-marker-alt  mr-2"></i>
							<span>{{reservation.agenceRetour}}</span>
						</p>
					</div>
				</div>
			</div>

			<!-- Right Column: Duration and Passengers -->
			<div class="col-md-6 pl-md-4 mt-4 mt-md-0">
				<div
					class="bg-light rounded p-3 h-100">
					<!-- Duration Information -->
					<div class="mb-4">
						<div class="d-flex align-items-center mb-2">
							<i class="fas fa-clock  mr-2"></i>
							<h6 class="font-weight-bold  mb-0">Durée</h6>
						</div>
						<p class="ml-4 mb-0 font-weight-medium ">
							{{reservation.duree}}
							jours
						</p>
					</div>

					<!-- Passenger Information -->
					<div>
						<div class="d-flex align-items-center mb-2">
							<i class="fas fa-users mr-2"></i>
							<h6 class="font-weight-bold  mb-0">Nombre de personnes</h6>
						</div>
						<div class="ml-4">
							<div class="row">
								<div class="col-6">
									<p class="mb-1">
										<i class="fas fa-user text-gray-600 mr-1"></i>
										<small class="text-gray-600">Adultes:</small>
									</p>
									<p class="ml-4 font-weight-medium ">
										{{reservation.client.infosResa ? reservation.client.infosResa.nbrAdultes : "non renseigné"}}
									</p>
								</div>
								<div class="col-6">
									<p class="mb-1">
										<i class="fas fa-child text-gray-600 mr-1"></i>
										<small class="text-gray-600">Enfants:</small>
									</p>
									<p class="ml-4 font-weight-medium ">
										{{reservation.client.infosResa ? reservation.client.infosResa.nbrEnfants : "non renseigné"}}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div
	class="card reservation-card mb-2">
	<!-- Conductors Section -->
	<div class="card-body p-4">
		<div class="d-flex justify-content-between align-items-center mb-3">
			<h5 class="card-title font-weight-bold text-red-800 d-flex align-items-center mb-0">
				<i class="fas fa-id-card text-red-400 mr-2"></i>
				Conducteurs
			</h5>
			{% if reservation.conducteursClient|length < 2 %}
				<button type="button" class="btn btn-link text-primary p-0" data-toggle="modal" data-target="#modalConducteur">
					<i class="fas fa-plus-circle mr-1"></i>
					Ajouter Conducteur
				</button>
			{% endif %}
		</div>

		<div class="space-y-3">
			{% set somme = 0  %}
			{% for conducteur in reservation.conducteursClient %}
				{% if conducteur.isPrincipal %}
					{% set somme = somme + 1  %}
				{% endif %}
			{% endfor %}

			{% for conducteur in reservation.conducteursClient %}
				<div class="conductor-card p-3 rounded mb-3 {% if conducteur.isPrincipal %}bg-gray-50{% else %}border border-gray-200{% endif %}">
					<div class="d-flex justify-content-between align-items-start">
						<div>
							<div class="d-flex align-items-center mb-1">
								<span class="font-weight-medium  mr-2">{{conducteur.nom}}
									{{conducteur.prenom}}</span>
								{% if conducteur.isPrincipal %}
									<span class="badge badge-warning">Principal</span>
								{% endif %}
							</div>
							<small class="text-gray-600 d-block">Permis:
								{{conducteur.numeroPermis}}</small>
							<small class="text-gray-600 d-block">Délivré:
								{{conducteur.dateDelivrance|date('m/Y')}}
								à
								{{conducteur.villeDelivrance}}</small>
						</div>
						<div class="d-flex align-items-center">
							{% if somme == 0  %}
								<a href="{{path('make_conducteur_principal', {'id': conducteur.id, 'id_resa': reservation.id})}}" class="btn btn-sm btn-outline-success mr-1">
									<i class="fas fa-star mr-1"></i>
									Rendre Principal
								</a>
							{% elseif conducteur.isPrincipal %}
								<a href="{{path('remove_conducteur_principal', {'id': conducteur.id, 'id_resa': reservation.id})}}" class="btn btn-sm btn-outline-secondary mr-1">
									<i class="fas fa-edit mr-1"></i>
									Retirer Principal
								</a>
							{% endif %}
							<a href="{{path('conducteur_edit', {'id': conducteur.id})}}" class="btn btn-sm btn-link text-gray-400 p-1" title="Modifier">
								<i class="fas fa-edit"></i>
							</a>
							<form method="post" action="{{ path('client_conducteur_remove_reservation', {'id': conducteur.id, 'id_resa' : reservation.id}) }}" onsubmit="return confirm('Êtes-vous sûre de vouloir supprimer ce conducteur ?');" class="d-inline">
								<input type="hidden" name="_method" value="DELETE">
								<input type="hidden" name="reservation" value="{{reservation.id}}">
								<input type="hidden" name="_token" value="{{ csrf_token('delete' ~ conducteur.id) }}">
								<button class="btn btn-sm btn-link text-gray-400 p-1" title="Supprimer">
									<i class="fas fa-trash-alt"></i>
								</button>
							</form>
						</div>
					</div>
				</div>
			{% endfor %}
		</div>
	</div>
</div>

<div
	class="card reservation-card mb-3 shadow-sm">
	<!-- Options & Guarantees Section -->
	<div class="card-body p-4">
		<h5 class="card-title font-weight-bold text-red-800 d-flex align-items-center mb-4">
			<i class="fas fa-check-circle text-red-400 mr-2"></i>
			Options & Garanties
		</h5>

		<div
			class="row">
			<!-- Left Column: Selected Options -->
			<div class="col-md-6 mb-3">
				<div class="bg-light rounded p-3 h-100">
					<h6 class=" font-weight-bold mb-3 border-bottom border-gray-200 pb-2">
						<i class="fas fa-plus-circle  mr-2"></i>
						Options Sélectionnées
					</h6>

					<ul class="list-unstyled mb-0">
						{% if reservation.conducteur %}
							<li class="d-flex justify-content-between align-items-center py-2 border-bottom border-gray-100">
								<div class="d-flex align-items-center">
									<span class="bullet-point bg-red-400 mr-2"></span>
									<span>Conducteur Additionnel</span>
								</div>
								<span class="font-weight-medium">{{prixConductSuppl}}€</span>
							</li>
						{% endif %}

						{% for option in reservation.devisOptions %}
							<li class="d-flex justify-content-between align-items-center py-2 {% if not loop.last %}border-bottom border-gray-100{% endif %}">
								<div class="d-flex align-items-center">
									<span class="bullet-point bg-red-400 mr-2"></span>
									<span>{{option.opt.appelation}}
										{% if option.opt.appelation matches '/Siège/' %}
											<span class="text-gray-600">(x{{ option.quantity}})</span>
										{% endif %}
									</span>
								</div>
								<span class="font-weight-medium">{{(option.opt.prix * option.quantity)|number_format(2,","," ")}}€</span>
							</li>
						{% else %}
							<li class="text-gray-600 py-2 text-center">
								<i class="fas fa-info-circle mr-1"></i>
								Aucune option sélectionnée
							</li>
						{% endfor %}
					</ul>
				</div>
			</div>

			<!-- Right Column: Insurance Coverage -->
			<div class="col-md-6">
				<div class="bg-light rounded p-3 h-100">
					<h6 class=" font-weight-bold mb-3 border-bottom border-gray-200 pb-2">
						<i class="fas fa-shield-alt mr-2"></i>
						Couverture Assurance
					</h6>

					<ul class="list-unstyled mb-0">
						{% for garantie in reservation.garanties %}
							<li class="d-flex justify-content-between align-items-center py-2 {% if not loop.last %}border-bottom border-gray-100{% endif %}">
								<div class="d-flex align-items-center">
									<span class="bullet-point bg-red-400 mr-2"></span>
									<span>{{garantie}}</span>
								</div>
								<span class="font-weight-medium">{{(garantie.prix)|number_format(2,","," ")}}€</span>
							</li>
						{% else %}
							<li class="text-gray-600 py-2 text-center">
								<i class="fas fa-info-circle mr-1"></i>
								Aucune garantie sélectionnée
							</li>
						{% endfor %}
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>

<div
	class="card reservation-card mb-3 shadow-sm">
	<!-- Payment Summary Section -->
	<div class="card-body p-4">
		<h5 class="card-title font-weight-bold text-red-800 d-flex align-items-center mb-4">
			<i class="fas fa-receipt text-red-400 mr-2"></i>
			Résumé des Paiements
		</h5>

		<div
			class="row">
			<!-- Amount Breakdown -->
			<div class="col-md-6 mb-4">
				<div class="bg-light rounded p-3 h-100">
					<h6 class=" font-weight-bold mb-3 border-bottom border-gray-200 pb-2">
						<i class="fas fa-calculator  mr-2"></i>
						Détail du Montant
					</h6>

					<ul class="list-unstyled payment-breakdown mb-0">
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Location Véhicule</span>
							</div>
							<span class="font-weight-medium">{{reservation.tarifVehicule}}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Options</span>
							</div>
							<span class="font-weight-medium">{{reservation.prixOptions}}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Garanties</span>
							</div>
							<span class="font-weight-medium">{{reservation.prixGaranties}}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Dépassements horaires</span>
							</div>
							<span class="font-weight-medium">0€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-200">
							<div class="d-flex align-items-center">
								<span class="font-weight-medium">Sous-total</span>
							</div>
							<span class="font-weight-medium">{{reservation.prix}}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Remise</span>
							</div>
							<span class="font-weight-medium">0€</span>
						</li>
						<li class="d-flex justify-content-between py-3 mt-2 bg-gray-100 rounded">
							<div class="d-flex align-items-center">
								<span class="font-weight-bold ">Montant Total</span>
							</div>
							<span class="font-weight-bold text-red-800">{{reservation.prix}}€</span>
						</li>
					</ul>
				</div>
			</div>

			<!-- Payment History -->
			<div class="col-md-6">
				<div class="bg-light rounded p-3 h-100">
					<h6 class=" font-weight-bold mb-3 border-bottom border-gray-200 pb-2">
						<i class="fas fa-history  mr-2"></i>
						Historique des Paiements
					</h6>

					<div class="mb-4">
						{% for paiement in reservation.paiements %}
							<div class="d-flex justify-content-between align-items-center py-2 {% if not loop.last %}border-bottom border-gray-100{% endif %}">
								<div>
									<div class="d-flex align-items-center">
										<i class="fas fa-money-bill-wave text-gray-600 mr-2"></i>
										<p class=" mb-0">{{paiement.datePaiement|date('d/m/Y H:i')}}</p>
									</div>
									<small class="text-gray-500 ml-4">{{paiement.modePaiement.libelle}}</small>
								</div>
								<span class="font-weight-medium badge badge-light">{{paiement.montant}}€</span>
							</div>
						{% else %}
							<div class="text-center text-gray-600 py-3">
								<i class="fas fa-info-circle mr-1"></i>
								Aucun paiement enregistré
							</div>
						{% endfor %}
					</div>

					{% if reservation.prix <= reservation.sommePaiements %}
						<div class="alert alert-success d-flex align-items-center mb-0">
							<i class="fas fa-check-circle mr-2"></i>
							<span class="font-weight-medium">Payé Intégralement</span>
						</div>
					{% else %}
						<div class="alert alert-warning mb-0">
							<div class="d-flex justify-content-between mb-2">
								<span>Total payé:</span>
								<strong>{{reservation.sommePaiements}}€</strong>
							</div>
							<div class="d-flex justify-content-between">
								<span>Solde restant:</span>
								<strong>{{reservation.prix - reservation.sommePaiements}}€</strong>
							</div>
						</div>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
</div></div></div></div></div>{% include "client/reservation/modalConducteur.html.twig" %}{% endblock %}{% block javascripts %}<!-- Bootstrap 4 JS and dependencies --><script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script><script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script><script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/js/bootstrap.min.js"></script><script>// Simple script to handle interactive elements
$(document).ready(function () { // Tooltip functionality
$('[title]').tooltip();

// Hover effects for conductor cards
$('.conductor-card').hover(function () {
$(this).addClass('shadow-sm');
}, function () {
$(this).removeClass('shadow-sm');
});

// Confirmation for delete actions
$('form[onsubmit*="confirm"]').on('submit', function (e) {
if (!confirm('Êtes-vous sûre de vouloir supprimer ce conducteur ?')) {
e.preventDefault();
return false;
}
});

// Print functionality
$('.btn:contains("Imprimer")').on('click', function () {
window.print();
});
});</script><!-- Datatables (if needed for other functionality) --><script src="../../admin/vendors/datatables.net/js/jquery.dataTables.min.js"></script><script src="../../admin/vendors/datatables.net-bs/js/dataTables.bootstrap.min.js"></script><script src="../../admin/vendors/datatables.net-buttons/js/dataTables.buttons.min.js"></script><script src="../../admin/vendors/datatables.net-buttons-bs/js/buttons.bootstrap.min.js"></script><script src="../../admin/vendors/datatables.net-buttons/js/buttons.flash.min.js"></script><script src="../../admin/vendors/datatables.net-buttons/js/buttons.html5.min.js"></script><script src="../../admin/vendors/datatables.net-buttons/js/buttons.print.min.js"></script><script src="../../admin/vendors/datatables.net-fixedheader/js/dataTables.fixedHeader.min.js"></script><script src="../../admin/vendors/datatables.net-keytable/js/dataTables.keyTable.min.js"></script><script src="../../admin/vendors/datatables.net-responsive/js/dataTables.responsive.min.js"></script><script src="../../admin/vendors/datatables.net-responsive-bs/js/responsive.bootstrap.js"></script><script src="../../admin/vendors/datatables.net-scroller/js/dataTables.scroller.min.js"></script><script src="../../admin/vendors/jszip/dist/jszip.min.js"></script><script src="../../admin/vendors/pdfmake/build/pdfmake.min.js"></script><script src="../../admin/vendors/pdfmake/build/vfs_fonts.js"></script><script src="../../js/admin/jspdf.min.js"></script><script src="../../js/admin/jspdf.plugin.autotable.js"></script><script src="../../js/admin/factureJsPDF.js"></script>{% endblock %}
