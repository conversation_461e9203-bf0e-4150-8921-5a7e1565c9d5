{% if type != 'devis' %}
<div class="card reservation-card mb-2">
	<!-- Conductors Section -->
	<div class="card-body p-4">
		<div class="d-flex justify-content-between align-items-center mb-3">
			<h5 class="card-title font-weight-bold text-red-800 d-flex align-items-center mb-0">
				<i class="fas fa-id-card text-red-400 mr-2"></i>
				Conducteurs
			</h5>
			{% if reservation.conducteursClient|length < 2 %}
				<button type="button" class="btn btn-link text-primary p-0" data-toggle="modal" data-target="#modalConducteur">
					<i class="fas fa-plus-circle mr-1"></i>
					Ajouter Conducteur
				</button>
			{% endif %}
		</div>

		<div class="space-y-3">
			{% set somme = 0 %}
			{% for conducteur in reservation.conducteursClient %}
				{% if conducteur.isPrincipal %}
					{% set somme = somme + 1 %}
				{% endif %}
			{% endfor %}

			{% for conducteur in reservation.conducteursClient %}
				<div class="conductor-card p-3 rounded mb-3 {% if conducteur.isPrincipal %}bg-gray-50{% else %}border border-gray-200{% endif %}">
					<div class="d-flex justify-content-between align-items-start">
						<div>
							<div class="d-flex align-items-center mb-1">
								<span class="font-weight-medium mr-2">{{ conducteur.nom }}
									{{ conducteur.prenom }}</span>
								{% if conducteur.isPrincipal %}
									<span class="badge badge-warning">Principal</span>
								{% endif %}
							</div>
							<small class="text-gray-600 d-block">Permis:
								{{ conducteur.numeroPermis }}</small>
							<small class="text-gray-600 d-block">Délivré:
								{{ conducteur.dateDelivrance|date('m/Y') }}
								à
								{{ conducteur.villeDelivrance }}</small>
						</div>
						<div class="d-flex align-items-center">
							{% if somme == 0 %}
								<a href="{{ path('make_conducteur_principal', {'id': conducteur.id, 'id_resa': reservation.id}) }}" class="btn btn-sm btn-outline-success mr-1">
									<i class="fas fa-star mr-1"></i>
									Rendre Principal
								</a>
							{% elseif conducteur.isPrincipal %}
								<a href="{{ path('remove_conducteur_principal', {'id': conducteur.id, 'id_resa': reservation.id}) }}" class="btn btn-sm btn-outline-secondary mr-1">
									<i class="fas fa-edit mr-1"></i>
									Retirer Principal
								</a>
							{% endif %}
							<a href="{{ path('conducteur_edit', {'id': conducteur.id}) }}" class="btn btn-sm btn-link text-gray-400 p-1" title="Modifier">
								<i class="fas fa-edit"></i>
							</a>
							<form method="post" action="{{ path('client_conducteur_remove_reservation', {'id': conducteur.id, 'id_resa' : reservation.id}) }}" onsubmit="return confirm('Êtes-vous sûre de vouloir supprimer ce conducteur ?');" class="d-inline">
								<input type="hidden" name="_method" value="DELETE">
								<input type="hidden" name="reservation" value="{{ reservation.id }}">
								<input type="hidden" name="_token" value="{{ csrf_token('delete' ~ conducteur.id) }}">
								<button class="btn btn-sm btn-link text-gray-400 p-1" title="Supprimer">
									<i class="fas fa-trash-alt"></i>
								</button>
							</form>
						</div>
					</div>
				</div>
			{% endfor %}
		</div>
	</div>
</div>
{% endif %}
