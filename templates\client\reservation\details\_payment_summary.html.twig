<div class="card reservation-card mb-3 shadow-sm">
	<!-- Payment Summary Section -->
	<div class="card-body p-4">
		<h5 class="card-title font-weight-bold text-red-800 d-flex align-items-center mb-4">
			<i class="fas fa-receipt text-red-400 mr-2"></i>
			Résumé des Paiements
		</h5>

		<div class="row">
			<!-- Amount Breakdown -->
			<div class="col-md-6 mb-4">
				<div class="bg-light rounded p-3 h-100">
					<h6 class="font-weight-bold mb-3 border-bottom border-gray-200 pb-2">
						<i class="fas fa-calculator mr-2"></i>
						Détail du Montant
					</h6>

					<ul class="list-unstyled payment-breakdown mb-0">
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Location Véhicule</span>
							</div>
							<span class="font-weight-medium">{{ reservation.tarifVehicule }}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Options</span>
							</div>
							<span class="font-weight-medium">{{ reservation.prixOptions }}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Garanties</span>
							</div>
							<span class="font-weight-medium">{{ reservation.prixGaranties }}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Dépassements horaires</span>
							</div>
							<span class="font-weight-medium">0€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-200">
							<div class="d-flex align-items-center">
								<span class="font-weight-medium">Sous-total</span>
							</div>
							<span class="font-weight-medium">{{ reservation.prix }}€</span>
						</li>
						<li class="d-flex justify-content-between py-2 border-bottom border-gray-100">
							<div class="d-flex align-items-center">
								<span class="bullet-point bg-red-400 mr-2"></span>
								<span>Remise</span>
							</div>
							<span class="font-weight-medium">0€</span>
						</li>
						<li class="d-flex justify-content-between py-3 mt-2 bg-gray-100 rounded">
							<div class="d-flex align-items-center">
								<span class="font-weight-bold">Montant Total</span>
							</div>
							<span class="font-weight-bold text-red-800">{{ reservation.prix }}€</span>
						</li>
					</ul>
				</div>
			</div>

			<!-- Payment History -->
			{% if type != 'devis' %}
			<div class="col-md-6">
				<div class="bg-light rounded p-3 h-100">
					<h6 class="font-weight-bold mb-3 border-bottom border-gray-200 pb-2">
						<i class="fas fa-history mr-2"></i>
						Historique des Paiements
					</h6>

					<div class="mb-4">
						{% for paiement in reservation.paiements %}
							<div class="d-flex justify-content-between align-items-center py-2 {% if not loop.last %}border-bottom border-gray-100{% endif %}">
								<div>
									<div class="d-flex align-items-center">
										<i class="fas fa-money-bill-wave text-gray-600 mr-2"></i>
										<p class="mb-0">{{ paiement.datePaiement|date('d/m/Y H:i') }}</p>
									</div>
									<small class="text-gray-500 ml-4">{{ paiement.modePaiement.libelle }}</small>
								</div>
								<span class="font-weight-medium badge badge-light">{{ paiement.montant }}€</span>
							</div>
						{% else %}
							<div class="text-center text-gray-600 py-3">
								<i class="fas fa-info-circle mr-1"></i>
								Aucun paiement enregistré
							</div>
						{% endfor %}
					</div>

					{% if reservation.prix <= reservation.sommePaiements %}
						<div class="alert alert-success d-flex align-items-center mb-0">
							<i class="fas fa-check-circle mr-2"></i>
							<span class="font-weight-medium">Payé Intégralement</span>
						</div>
					{% else %}
						<div class="alert alert-warning mb-0">
							<div class="d-flex justify-content-between mb-2">
								<span>Total payé:</span>
								<strong>{{ reservation.sommePaiements }}€</strong>
							</div>
							<div class="d-flex justify-content-between">
								<span>Solde restant:</span>
								<strong>{{ reservation.prix - reservation.sommePaiements }}€</strong>
							</div>
						</div>
					{% endif %}
				</div>
			</div>
			{% endif %}
		</div>
	</div>
</div>
